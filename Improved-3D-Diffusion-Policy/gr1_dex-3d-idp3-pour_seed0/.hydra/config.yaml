task:
  name: box
  shape_meta:
    obs:
      front_image:
        shape:
        - 3
        - 84
        - 84
        type: rgb
      right_image:
        shape:
        - 3
        - 84
        - 84
        type: rgb
      front_point_cloud:
        shape:
        - 10000
        - 6
        type: point_cloud
      right_point_cloud:
        shape:
        - 10000
        - 6
        type: point_cloud
      agent_pos:
        shape:
        - 32
        type: low_dim
    action:
      shape:
      - 25
  dataset:
    _target_: diffusion_policy_3d.dataset.gr1_dex_dataset_multicam.GR1DexDatasetMultiCam
    zarr_path: /home/<USER>/projects/gr1-learning-real/raw_data/cap_pour3-env_qpos-qpos-zarr
    horizon: ${horizon}
    pad_before: ${eval:'${n_obs_steps}-1'}
    pad_after: ${eval:'${n_action_steps}-1'}
    seed: 42
    val_ratio: 0.0
    max_train_episodes: 90
    use_language: ${use_language}
    use_img: ${use_image}
    use_front_cam: ${policy.pointcloud_encoder_cfg.use_front_cam}
    num_points_front: ${policy.pointcloud_encoder_cfg.num_points_front}
    use_right_cam: ${policy.pointcloud_encoder_cfg.use_right_cam}
    num_points_right: ${policy.pointcloud_encoder_cfg.num_points_right}
name: train_diffusion_unet_hybrid
_target_: diffusion_policy_3d.workspace.train_diffusion_unet_hybrid_pointcloud_workspace.TrainDiffusionUnetHybridPointcloudWorkspace
task_name: ${task.name}
shape_meta: ${task.shape_meta}
exp_name: gr1_dex-3d-idp3-pour3
n_obs_steps: 2
horizon: 16
n_action_steps: 15
n_latency_steps: 0
dataset_obs_steps: ${n_obs_steps}
keypoint_visible_rate: 1.0
obs_as_global_cond: true
use_language: false
use_image: false
policy:
  _target_: diffusion_policy_3d.policy.diffusion_pointcloud_policy.DiffusionPointcloudPolicy
  use_point_crop: true
  use_down_condition: true
  use_mid_condition: true
  use_up_condition: true
  use_image: false
  diffusion_step_embed_dim: 128
  down_dims:
  - 256
  - 512
  - 1024
  crop_shape:
  - 80
  - 80
  horizon: ${horizon}
  kernel_size: 5
  n_action_steps: ${n_action_steps}
  n_groups: 8
  n_obs_steps: ${n_obs_steps}
  use_language: ${use_language}
  noise_scheduler:
    _target_: diffusers.schedulers.scheduling_ddim.DDIMScheduler
    num_train_timesteps: 50
    beta_start: 0.0001
    beta_end: 0.02
    beta_schedule: squaredcos_cap_v2
    clip_sample: true
    set_alpha_to_one: true
    steps_offset: 0
    prediction_type: sample
  num_inference_steps: 10
  obs_as_global_cond: true
  shape_meta: ${shape_meta}
  use_pc_color: false
  pointnet_type: multi_stage_pointnet
  point_downsample: true
  se3_augmentation_cfg:
    use_aug: false
    rotation: false
    rotation_angle:
    - 15
    - 15
    - 15
    translation: true
    translation_scale: 0.01
    jitter: true
    jitter_scale: 0.01
  use_front_cam: true
  use_right_cam: false
  pointcloud_encoder_cfg:
    in_channels: 3
    out_channels: 128
    use_layernorm: true
    final_norm: layernorm
    normal_channel: false
    use_front_cam: ${policy.use_front_cam}
    num_points_front: 4096
    use_right_cam: ${policy.use_right_cam}
    num_points_right: 1024
    use_multi_scale: false
ema:
  _target_: diffusion_policy_3d.model.diffusion.ema_model.EMAModel
  update_after_step: 0
  inv_gamma: 1.0
  power: 0.75
  min_value: 0.0
  max_value: 0.9999
dataloader:
  batch_size: 64
  num_workers: 8
  shuffle: true
  pin_memory: true
  persistent_workers: false
val_dataloader:
  batch_size: 64
  num_workers: 8
  shuffle: false
  pin_memory: true
  persistent_workers: false
optimizer:
  _target_: torch.optim.AdamW
  lr: 0.0001
  betas:
  - 0.95
  - 0.999
  eps: 1.0e-08
  weight_decay: 1.0e-06
training:
  device: cuda:0
  seed: 0
  debug: false
  resume: true
  lr_scheduler: cosine
  lr_warmup_steps: 500
  num_epochs: 301
  gradient_accumulate_every: 1
  use_ema: true
  rollout_every: 400
  checkpoint_every: 100
  val_every: 100
  sample_every: 5
  max_train_steps: null
  max_val_steps: null
  tqdm_interval_sec: 1.0
  save_video: true
logging:
  group: ${exp_name}
  id: null
  mode: offline
  name: ${training.seed}
  project: humanoid_mimic
  resume: true
  tags:
  - train_diffusion_unet_hybrid
  - dexdeform
checkpoint:
  save_ckpt: true
  topk:
    monitor_key: test_mean_score
    mode: max
    k: 0
    format_str: epoch={epoch:04d}-test_mean_score={test_mean_score:.3f}.ckpt
  save_last_ckpt: true
  save_last_snapshot: false
multi_run:
  run_dir: data/outputs/${now:%Y.%m.%d}/${now:%H.%M.%S}_${name}_${task_name}
  wandb_name_base: ${now:%Y.%m.%d-%H.%M.%S}_${name}_${task_name}
