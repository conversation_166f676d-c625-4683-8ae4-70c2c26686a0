# bash convert_data.sh


save_img=1
save_depth=0


demo_path=/home/<USER>/code/opensource/Improved-3D-Diffusion-Policy/raw_pour
save_path=/home/<USER>/code/opensource/Improved-3D-Diffusion-Policy/raw_pour_converted

python /home/<USER>/code/opensource/Improved-3D-Diffusion-Policy/Humanoid-Teleoperation-main/humanoid_teleoperation/scripts/convert_demos.py \
                                --demo_dir ${demo_path} \
                                --save_dir ${save_path} \
                                --save_img ${save_img} \
                                --save_depth ${save_depth} \
